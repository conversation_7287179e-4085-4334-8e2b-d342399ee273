package com.building.servlet;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.building.model.Camera;
import com.building.model.PersonRecord;
import com.building.service.CameraService;
import com.building.service.impl.CameraServiceImpl;

/**
 * 摄像头详情Servlet
 * 用于处理摄像头详情页面的请求
 */
@WebServlet("/camera/detail")
public class CameraDetailServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private CameraService cameraService;

    @Override
    public void init() throws ServletException {
        cameraService = new CameraServiceImpl();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 检查用户是否登录
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }

        // 获取摄像头ID
        String idParam = request.getParameter("id");
        if (idParam == null || idParam.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/camera/list");
            return;
        }

        try {
            int cameraId = Integer.parseInt(idParam);
            System.out.println("获取摄像头详情，ID: " + cameraId);

            // 获取摄像头信息
            Camera camera = cameraService.getCameraById(cameraId);
            if (camera == null) {
                System.err.println("摄像头不存在，ID: " + cameraId);
                response.sendRedirect(request.getContextPath() + "/camera/list");
                return;
            }

            System.out.println("摄像头信息获取成功: " + camera.getName());

            // 如果摄像头属于某个房间，获取该房间的人员记录历史
            if (camera.getRoomId() > 0) {
                System.out.println("获取房间人员记录，房间ID: " + camera.getRoomId());

                try {
                    List<PersonRecord> records = cameraService.getPersonRecordHistory(camera.getRoomId(), 10);
                    if (records != null && !records.isEmpty()) {
                        request.setAttribute("personRecords", records);
                        System.out.println("人员记录获取成功，数量: " + records.size());
                    } else {
                        System.out.println("暂无人员记录");
                        request.setAttribute("personRecords", new ArrayList<>());
                    }

                    // 获取最新的人员记录
                    PersonRecord latestRecord = cameraService.getLatestPersonRecord(camera.getRoomId());
                    if (latestRecord != null) {
                        request.setAttribute("latestRecord", latestRecord);
                        System.out.println("最新人员记录获取成功");
                    } else {
                        System.out.println("暂无最新人员记录");
                    }
                } catch (Exception e) {
                    System.err.println("获取人员记录时发生错误: " + e.getMessage());
                    e.printStackTrace();
                    // 设置空列表，避免JSP错误
                    request.setAttribute("personRecords", new ArrayList<>());
                }
            } else {
                System.out.println("摄像头未分配房间");
                request.setAttribute("personRecords", new ArrayList<>());
            }

            // 设置请求属性
            request.setAttribute("camera", camera);

            // 转发到摄像头详情页面
            System.out.println("转发到摄像头详情页面");
            request.getRequestDispatcher("/WEB-INF/views/camera/detail.jsp").forward(request, response);

        } catch (NumberFormatException e) {
            System.err.println("摄像头ID格式错误: " + idParam);
            response.sendRedirect(request.getContextPath() + "/camera/list");
        } catch (Exception e) {
            System.err.println("摄像头详情页面发生未知错误: " + e.getMessage());
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/camera/list");
        }
    }
}
