package com.building.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.building.model.Camera;
import com.building.model.Room;
import com.building.util.DBUtil;

/**
 * 摄像头数据访问对象
 * 用于处理摄像头相关的数据库操作
 */
public class CameraDao {

    /**
     * 获取所有摄像头
     * @return 摄像头列表
     */
    public List<Camera> getAllCameras() {
        List<Camera> cameras = new ArrayList<>();
        String sql = "SELECT c.*, r.room_number, r.floor_number, r.room_type " +
                    "FROM camera c LEFT JOIN room r ON c.room_id = r.id " +
                    "ORDER BY c.id";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                Camera camera = mapResultSetToCamera(rs);

                // 设置房间信息
                if (rs.getObject("room_id") != null) {
                    Room room = new Room();
                    room.setId(rs.getInt("room_id"));
                    room.setRoomNumber(rs.getString("room_number"));
                    room.setFloorNumber(rs.getInt("floor_number"));
                    room.setRoomType(rs.getString("room_type"));
                    camera.setRoom(room);
                }

                cameras.add(camera);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return cameras;
    }

    /**
     * 根据ID获取摄像头
     * @param id 摄像头ID
     * @return 摄像头对象，如果不存在则返回null
     */
    public Camera getCameraById(int id) {
        String sql = "SELECT c.*, r.room_number, r.floor_number, r.room_type " +
                    "FROM camera c LEFT JOIN room r ON c.room_id = r.id " +
                    "WHERE c.id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Camera camera = mapResultSetToCamera(rs);

                    // 设置房间信息
                    if (rs.getObject("room_id") != null) {
                        Room room = new Room();
                        room.setId(rs.getInt("room_id"));
                        room.setRoomNumber(rs.getString("room_number"));
                        room.setFloorNumber(rs.getInt("floor_number"));
                        room.setRoomType(rs.getString("room_type"));
                        camera.setRoom(room);
                    }

                    return camera;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 根据房间ID获取摄像头列表
     * @param roomId 房间ID
     * @return 摄像头列表
     */
    public List<Camera> getCamerasByRoomId(int roomId) {
        List<Camera> cameras = new ArrayList<>();
        String sql = "SELECT * FROM camera WHERE room_id = ? ORDER BY id";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, roomId);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Camera camera = mapResultSetToCamera(rs);
                    cameras.add(camera);
                }
            }
        } catch (SQLException e) {
            System.err.println("根据房间ID获取摄像头列表时发生错误：" + e.getMessage());
            e.printStackTrace();
        }

        return cameras;
    }

    /**
     * 检查IP地址是否已存在
     * @param ipAddress IP地址
     * @param excludeId 排除的摄像头ID（用于更新时排除自身）
     * @return 是否存在
     */
    public boolean isIpAddressExists(String ipAddress, int excludeId) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return false;
        }

        String sql = "SELECT COUNT(*) FROM camera WHERE ip_address = ? AND id != ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, ipAddress.trim());
            pstmt.setInt(2, excludeId);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            System.err.println("检查IP地址是否存在时发生错误：" + e.getMessage());
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 添加摄像头
     * @param camera 摄像头对象
     * @return 是否添加成功
     */
    public boolean addCamera(Camera camera) {
        // 参数验证
        if (camera == null) {
            System.err.println("添加摄像头失败：摄像头对象为空");
            return false;
        }

        // 检查IP地址是否已存在
        if (isIpAddressExists(camera.getIpAddress(), 0)) {
            System.err.println("添加摄像头失败：IP地址已存在 - " + camera.getIpAddress());
            return false;
        }

        String sql = "INSERT INTO camera (name, ip_address, port, username, password, rtsp_url, " +
                    "location, brand, model, status, room_id) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, camera.getName());
            pstmt.setString(2, camera.getIpAddress());
            pstmt.setInt(3, camera.getPort());
            pstmt.setString(4, camera.getUsername());
            pstmt.setString(5, camera.getPassword());
            pstmt.setString(6, camera.getRtspUrl());
            pstmt.setString(7, camera.getLocation());
            pstmt.setString(8, camera.getBrand());
            pstmt.setString(9, camera.getModel());
            pstmt.setInt(10, camera.getStatus());

            if (camera.getRoomId() > 0) {
                pstmt.setInt(11, camera.getRoomId());
            } else {
                pstmt.setNull(11, java.sql.Types.INTEGER);
            }

            int result = pstmt.executeUpdate();
            if (result > 0) {
                System.out.println("摄像头添加成功：" + camera.getName() + " (" + camera.getIpAddress() + ")");
                return true;
            } else {
                System.err.println("摄像头添加失败：数据库操作返回0行");
                return false;
            }
        } catch (SQLException e) {
            System.err.println("添加摄像头时发生数据库错误：" + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新摄像头
     * @param camera 摄像头对象
     * @return 是否更新成功
     */
    public boolean updateCamera(Camera camera) {
        // 参数验证
        if (camera == null || camera.getId() <= 0) {
            System.err.println("更新摄像头失败：摄像头对象为空或ID无效");
            return false;
        }

        // 检查IP地址是否已被其他摄像头使用
        if (isIpAddressExists(camera.getIpAddress(), camera.getId())) {
            System.err.println("更新摄像头失败：IP地址已被其他摄像头使用 - " + camera.getIpAddress());
            return false;
        }

        String sql = "UPDATE camera SET name=?, ip_address=?, port=?, username=?, password=?, " +
                    "rtsp_url=?, location=?, brand=?, model=?, status=?, room_id=? " +
                    "WHERE id=?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, camera.getName());
            pstmt.setString(2, camera.getIpAddress());
            pstmt.setInt(3, camera.getPort());
            pstmt.setString(4, camera.getUsername());
            pstmt.setString(5, camera.getPassword());
            pstmt.setString(6, camera.getRtspUrl());
            pstmt.setString(7, camera.getLocation());
            pstmt.setString(8, camera.getBrand());
            pstmt.setString(9, camera.getModel());
            pstmt.setInt(10, camera.getStatus());

            if (camera.getRoomId() > 0) {
                pstmt.setInt(11, camera.getRoomId());
            } else {
                pstmt.setNull(11, java.sql.Types.INTEGER);
            }

            pstmt.setInt(12, camera.getId());

            int result = pstmt.executeUpdate();
            if (result > 0) {
                System.out.println("摄像头更新成功：" + camera.getName() + " (" + camera.getIpAddress() + ")");
                return true;
            } else {
                System.err.println("摄像头更新失败：未找到指定的摄像头ID - " + camera.getId());
                return false;
            }
        } catch (SQLException e) {
            System.err.println("更新摄像头时发生数据库错误：" + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除摄像头
     * @param id 摄像头ID
     * @return 是否删除成功
     */
    public boolean deleteCamera(int id) {
        String sql = "DELETE FROM camera WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, id);

            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 更新摄像头状态
     * @param id 摄像头ID
     * @param status 状态：0-离线 1-在线
     * @return 是否更新成功
     */
    public boolean updateCameraStatus(int id, int status) {
        String sql = "UPDATE camera SET status = ?, last_online_time = NOW() WHERE id = ?";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setInt(1, status);
            pstmt.setInt(2, id);

            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取摄像头统计信息
     * @return 统计信息Map
     */
    public Map<String, Integer> getCameraStats() {
        Map<String, Integer> stats = new HashMap<>();
        String sql = "SELECT " +
                    "COUNT(*) AS total, " +
                    "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS online, " +
                    "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS offline " +
                    "FROM camera";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {

            if (rs.next()) {
                stats.put("total", rs.getInt("total"));
                stats.put("online", rs.getInt("online"));
                stats.put("offline", rs.getInt("offline"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return stats;
    }

    /**
     * 将ResultSet映射到Camera对象
     * @param rs ResultSet
     * @return Camera对象
     * @throws SQLException 如果发生SQL异常
     */
    private Camera mapResultSetToCamera(ResultSet rs) throws SQLException {
        Camera camera = new Camera();
        camera.setId(rs.getInt("id"));
        camera.setName(rs.getString("name"));
        camera.setIpAddress(rs.getString("ip_address"));
        camera.setPort(rs.getInt("port"));
        camera.setUsername(rs.getString("username"));
        camera.setPassword(rs.getString("password"));
        camera.setRtspUrl(rs.getString("rtsp_url"));
        camera.setLocation(rs.getString("location"));
        camera.setBrand(rs.getString("brand"));
        camera.setModel(rs.getString("model"));
        camera.setStatus(rs.getInt("status"));
        camera.setLastOnlineTime(rs.getString("last_online_time"));

        // 处理可能为null的字段
        Object roomId = rs.getObject("room_id");
        if (roomId != null) {
            camera.setRoomId(rs.getInt("room_id"));
        }

        camera.setCreateTime(rs.getString("create_time"));
        camera.setUpdateTime(rs.getString("update_time"));

        return camera;
    }
}
