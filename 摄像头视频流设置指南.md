# 摄像头视频流设置指南

## 问题分析

您遇到的问题是浏览器无法直接播放RTSP视频流。这是因为：

1. **浏览器限制**：现代浏览器不支持直接播放RTSP协议
2. **协议转换**：需要将RTSP流转换为浏览器支持的格式（如HLS、WebRTC）
3. **当前实现**：系统只显示静态图片，没有真实的视频流

## 解决方案

### 方案一：使用Node Media Server（推荐）

#### 步骤1：安装Node.js和FFmpeg

1. **安装Node.js**：
   - 访问 https://nodejs.org/ 下载并安装
   - 验证安装：`node --version`

2. **安装FFmpeg**：
   - 访问 https://ffmpeg.org/download.html 下载
   - 解压到 `C:\ffmpeg\`
   - 添加 `C:\ffmpeg\bin` 到系统PATH环境变量

#### 步骤2：设置流媒体服务器

1. **安装依赖**：
   ```powershell
   npm install
   ```

2. **修改配置**：
   编辑 `rtsp-to-hls-server.js` 文件，更新您的摄像头RTSP地址：
   ```javascript
   edge: 'rtsp://admin:admin@*************:554/h264/ch1/main/av_stream'
   ```

3. **启动服务器**：
   ```powershell
   npm start
   ```

#### 步骤3：测试视频流

1. **启动流媒体服务器**后，访问：
   - HLS播放地址：`http://localhost:8888/live/camera1.m3u8`
   - 可以用VLC播放器测试此地址

2. **在您的Web系统中**：
   - 重新加载摄像头视频页面
   - 系统会自动尝试播放HLS流

### 方案二：使用VLC播放器（临时方案）

1. **复制RTSP地址**：
   - 在视频流页面点击"复制"按钮
   - 复制显示的RTSP地址

2. **使用VLC播放**：
   - 打开VLC播放器
   - 选择"媒体" → "打开网络串流"
   - 粘贴RTSP地址并播放

### 方案三：使用WebRTC（高级方案）

如果需要更低延迟的实时视频流，可以考虑：
1. 使用Kurento媒体服务器
2. 配置WebRTC网关
3. 实现P2P视频传输

## 故障排除

### 常见问题

1. **FFmpeg路径错误**：
   - 确保FFmpeg安装在 `C:\ffmpeg\bin\ffmpeg.exe`
   - 或修改 `rtsp-to-hls-server.js` 中的路径

2. **摄像头连接失败**：
   - 检查IP地址：`*************`
   - 验证用户名密码：`admin/admin`
   - 确保摄像头和服务器在同一网络

3. **端口被占用**：
   - 检查1935端口（RTMP）
   - 检查8888端口（HTTP）
   - 使用 `netstat -an | findstr :8888` 检查端口状态

4. **防火墙阻止**：
   - 允许1935和8888端口通过防火墙
   - 临时关闭防火墙测试

### 调试步骤

1. **检查RTSP连接**：
   ```powershell
   # 使用FFmpeg测试RTSP流
   ffmpeg -i rtsp://admin:admin@*************:554/h264/ch1/main/av_stream -t 10 -f null -
   ```

2. **检查HLS生成**：
   - 启动Node Media Server后
   - 访问 `http://localhost:8888/live/camera1.m3u8`
   - 应该看到HLS播放列表

3. **浏览器控制台**：
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息
   - 查看Network标签页的网络请求

## 性能优化

1. **降低延迟**：
   - 调整GOP缓存设置
   - 使用更小的chunk_size
   - 启用硬件加速

2. **提高稳定性**：
   - 配置自动重连机制
   - 监控流媒体服务器状态
   - 实现故障转移

## 安全考虑

1. **认证机制**：
   - 为流媒体服务器添加认证
   - 使用HTTPS传输
   - 限制访问IP范围

2. **网络安全**：
   - 配置防火墙规则
   - 使用VPN连接
   - 定期更新摄像头固件

## 下一步

1. **测试当前修改**：重新启动您的Java Web应用
2. **安装Node.js环境**：按照步骤1安装必要软件
3. **配置流媒体服务器**：按照步骤2设置转换服务
4. **验证视频播放**：确认浏览器中可以看到实时视频

如果遇到问题，请检查控制台输出和错误日志。
