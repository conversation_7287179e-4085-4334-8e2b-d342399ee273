# 摄像头详情页面访问问题诊断

## 🔍 问题分析

### 问题描述
访问 `http://localhost:8080/jspPj002/camera/detail?id=1` 时，不管ID等于多少，都没有对应的页面，并且错误信息提到 `stream-content.jsp`。

### 问题根因分析
1. **页面跳转链接问题**: 详情页面中的"全屏查看"按钮可能触发了到视频流页面的跳转
2. **数据空值问题**: `stream-content.jsp` 中可能存在空值处理不当
3. **数据库数据问题**: 可能数据库中没有对应ID的摄像头数据

## 🔧 已修复的问题

### 1. stream-content.jsp 空值处理
**修复前的问题代码:**
```jsp
${camera.name}
${camera.location}
${camera.rtspUrl}
```

**修复后:**
```jsp
${not empty camera.name ? camera.name : '未命名摄像头'}
${not empty camera.location ? camera.location : '未设置位置'}
${not empty camera.rtspUrl ? camera.rtspUrl : '未设置RTSP地址'}
```

### 2. 条件渲染优化
**添加了RTSP URL的条件检查:**
```jsp
<c:if test="${not empty camera.rtspUrl}">
    <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyRtspUrl()">
        <i class="bi bi-clipboard"></i> 复制
    </button>
</c:if>
```

## 📋 完整的访问流程

### 1. URL映射关系
```
/camera/detail?id=1  →  CameraDetailServlet  →  detail.jsp  →  detail-content.jsp
/camera/stream?id=1  →  VideoStreamServlet   →  stream.jsp →  stream-content.jsp
```

### 2. 页面跳转逻辑
```javascript
// 详情页面中的"全屏查看"按钮
document.getElementById('viewStreamBtn').addEventListener('click', function() {
    const cameraId = this.getAttribute('data-camera-id');
    window.location.href = '${pageContext.request.contextPath}/camera/stream?id=' + cameraId;
});
```

## 🚀 测试步骤

### 1. 启动应用
```bash
mvn clean compile
mvn tomcat7:run
```

### 2. 测试摄像头详情页面
```
http://localhost:8080/jspPj002/camera/detail?id=1
```

### 3. 测试视频流页面
```
http://localhost:8080/jspPj002/camera/stream?id=1
```

## 🔍 故障排除指南

### 如果详情页面无法访问

#### 1. 检查数据库连接
```sql
-- 检查摄像头表是否有数据
SELECT * FROM cameras LIMIT 5;

-- 检查特定ID的摄像头
SELECT * FROM cameras WHERE id = 1;
```

#### 2. 检查Servlet日志
在CameraDetailServlet中已添加详细日志：
```
获取摄像头详情，ID: 1
摄像头信息获取成功: Camera 01
获取房间人员记录，房间ID: 0
摄像头未分配房间
转发到摄像头详情页面
```

#### 3. 检查用户登录状态
确保用户已登录，否则会重定向到登录页面：
```java
if (session.getAttribute("user") == null) {
    response.sendRedirect(request.getContextPath() + "/login.jsp");
    return;
}
```

### 如果视频流页面无法访问

#### 1. 检查VideoStreamServlet
确保VideoStreamServlet正常工作：
```java
@WebServlet("/camera/stream")
public class VideoStreamServlet extends HttpServlet {
    // 处理视频流页面请求
}
```

#### 2. 检查stream-content.jsp
现在已修复空值处理问题，应该能正常显示。

## 🎯 可能的问题和解决方案

### 问题1: 数据库中没有摄像头数据
**解决方案**: 添加测试数据
```sql
INSERT INTO cameras (name, ip_address, port, location, brand, model, status, rtsp_url, room_id) 
VALUES ('测试摄像头01', '*************', 554, '大厅入口', '海康威视', 'DS-2CD2T47G1', 1, 'rtsp://*************:554/stream1', 0);
```

### 问题2: 摄像头ID不存在
**解决方案**: CameraDetailServlet会自动重定向到摄像头列表
```java
if (camera == null) {
    System.err.println("摄像头不存在，ID: " + cameraId);
    response.sendRedirect(request.getContextPath() + "/camera/list");
    return;
}
```

### 问题3: 页面渲染错误
**解决方案**: 
1. 检查浏览器开发者工具Console
2. 查看服务器日志
3. 验证JSP语法

## ✅ 验证清单

### 详情页面功能
- [ ] 页面正常加载，无500错误
- [ ] 摄像头基本信息正确显示
- [ ] 状态指示器正常工作
- [ ] 控制按钮正常响应
- [ ] 人员记录图表正常显示
- [ ] 返回按钮正确跳转

### 视频流页面功能
- [ ] 从详情页面正确跳转
- [ ] 视频流信息正确显示
- [ ] RTSP地址正确显示
- [ ] 控制按钮正常工作
- [ ] 返回按钮正确跳转

## 🎉 总结

经过修复，现在系统应该能够：

1. **正确处理摄像头详情页面访问**
2. **安全处理所有空值情况**
3. **提供详细的错误日志**
4. **正确跳转到视频流页面**

### 下一步操作
1. 启动应用并测试
2. 检查数据库中是否有摄像头数据
3. 如果仍有问题，查看服务器控制台日志
4. 使用浏览器开发者工具检查网络请求

现在应该能够正常访问摄像头详情页面了！
